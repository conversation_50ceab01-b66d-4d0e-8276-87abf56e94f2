// 动作名称到后端命令的映射表
// 这个文件包含了所有动作的后端调用信息，用于批量更新动作定义

import type { ActionParams, ActionContext } from "../types/action";

// 动作映射接口
export interface ActionMapping {
  invokeCommand: string;
  buildParams: (params: ActionParams, context: ActionContext) => any;
}

// 时间字符串转秒数的工具函数
function timeStringToSeconds(timeStr: string): number {
  const parts = timeStr.split(":");
  const hours = parseInt(parts[0]) || 0;
  const minutes = parseInt(parts[1]) || 0;
  const seconds = parseInt(parts[2]) || 0;
  return hours * 3600 + minutes * 60 + seconds;
}

// 动作映射表
export const actionMappings: Record<string, ActionMapping> = {
  // 裁剪类动作
  "trim-start": {
    invokeCommand: "trim_video_start",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      startDuration: parseFloat(params.startDuration) || 10.0,
    }),
  },

  "trim-end": {
    invokeCommand: "trim_video_end",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      endTime: parseFloat(params.endTime) || 10.0,
    }),
  },

  "trim-segment": {
    invokeCommand: "trim_and_concat_segments",
    buildParams: (params: ActionParams, context: ActionContext) => {
      const segmentTuples = params.segments.map((segment: any) => [
        timeStringToSeconds(segment.startTimeStr),
        timeStringToSeconds(segment.endTimeStr),
      ]);
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        segments: segmentTuples,
      };
    },
  },

  "exclude-segment": {
    invokeCommand: "exclude_and_concat_segments",
    buildParams: (params: ActionParams, context: ActionContext) => {
      const segmentTuples = params.segments.map((segment: any) => [
        timeStringToSeconds(segment.startTimeStr),
        timeStringToSeconds(segment.endTimeStr),
      ]);
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        segments: segmentTuples,
      };
    },
  },

  "crop-video": {
    invokeCommand: "crop_video",
    buildParams: (params: ActionParams, context: ActionContext) => {
      const x = parseInt(params.x);
      const y = parseInt(params.y);
      const width = parseInt(params.width);
      const height = parseInt(params.height);
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        x: isNaN(x) ? 0 : x,
        y: isNaN(y) ? 0 : y,
        width: isNaN(width) ? 640 : width,
        height: isNaN(height) ? 480 : height,
      };
    },
  },

  // 颜色调整类动作
  "adjust-brightness": {
    invokeCommand: "adjust_brightness",
    buildParams: (params: ActionParams, context: ActionContext) => {
      const brightness = parseFloat(params.brightness);
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        brightness: isNaN(brightness) ? 0 : brightness,
      };
    },
  },

  "adjust-contrast": {
    invokeCommand: "adjust_contrast",
    buildParams: (params: ActionParams, context: ActionContext) => {
      const contrast = parseFloat(params.contrast);
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        contrast: isNaN(contrast) ? 1.0 : contrast,
      };
    },
  },

  "adjust-saturation": {
    invokeCommand: "adjust_saturation",
    buildParams: (params: ActionParams, context: ActionContext) => {
      const saturation = parseFloat(params.saturation);
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        saturation: isNaN(saturation) ? 1.0 : saturation,
      };
    },
  },

  "adjust-hue": {
    invokeCommand: "adjust_hue",
    buildParams: (params: ActionParams, context: ActionContext) => {
      const hue = parseFloat(params.hue);
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        hue: isNaN(hue) ? 0 : hue,
      };
    },
  },

  "adjust-gamma": {
    invokeCommand: "adjust_gamma",
    buildParams: (params: ActionParams, context: ActionContext) => {
      const gamma = parseFloat(params.gamma);
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        gamma: isNaN(gamma) ? 1.0 : gamma,
      };
    },
  },

  "white-balance": {
    invokeCommand: "adjust_white_balance",
    buildParams: (params: ActionParams, context: ActionContext) => {
      const temperature = parseFloat(params.temperature);
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        temperature: isNaN(temperature) ? 6500 : temperature,
      };
    },
  },

  // 音频处理类动作
  "adjust-volume": {
    invokeCommand: "adjust_volume",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      volume: parseFloat(params.volume) || 1.0,
    }),
  },

  "mute-audio": {
    invokeCommand: "mute_audio",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
    }),
  },

  "extract-audio": {
    invokeCommand: "extract_audio",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      format: params.format || "mp3",
    }),
  },

  "add-background-music": {
    invokeCommand: "add_bgmusic",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      bgmPath: params.audioFile,
      loopBgm: !!params.loopBgm,
      bgmVolume: params.bgmVolume ?? 0.5,
    }),
  },

  "replace-audio": {
    invokeCommand: "audio_replace",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      audioPath: params.audioFile,
      loopAudio: !!params.loopAudio,
      audioVolume: params.audioVolume ?? 0.5,
    }),
  },

  // 变换类动作
  "rotate-video": {
    invokeCommand: "rotate_video",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      angle:
        params.angle === "custom"
          ? parseInt(params.customAngle) || 0
          : parseInt(params.angle) || 90,
    }),
  },

  "flip-horizontal": {
    invokeCommand: "flip_video_horizontal",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
    }),
  },

  "flip-vertical": {
    invokeCommand: "flip_video_vertical",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
    }),
  },

  "scale-video": {
    invokeCommand: "scale_video",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      scale: parseFloat(params.scale) || 1.0,
    }),
  },

  // 特效类动作
  "adjust-speed": {
    invokeCommand: "adjust_speed",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      speed: parseFloat(params.speed) || 1.0,
    }),
  },

  "fade-in": {
    invokeCommand: "fade_in",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      duration: parseFloat(params.duration) || 1.0,
    }),
  },

  "fade-out": {
    invokeCommand: "fade_out",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      duration: parseFloat(params.duration) || 1.0,
    }),
  },

  "reverse-video": {
    invokeCommand: "reverse_video",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      keepAudio: !!params.keepAudio,
    }),
  },

  "blur-effect": {
    invokeCommand: "blur_video",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      intensity: parseFloat(params.intensity) || 5.0,
    }),
  },

  "sharpen-effect": {
    invokeCommand: "sharpen_video",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      intensity: parseFloat(params.intensity) || 1.0,
    }),
  },

  "emboss-effect": {
    invokeCommand: "apply_emboss",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
    }),
  },

  "mosaic-effect": {
    invokeCommand: "apply_mosaic",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      blockSize: parseInt(params.blockSize) || 10,
    }),
  },

  // 水印类动作
  "text-watermark": {
    invokeCommand: "add_text_watermark",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      text: params.text || "水印文字",
      position: params.position || "右下",
      fontSize: parseInt(params.fontSize) || 24,
      fontColor: params.fontColor || "white",
      opacity: parseFloat(params.opacity) || 0.8,
      customX: params.customX ? parseInt(params.customX) : null,
      customY: params.customY ? parseInt(params.customY) : null,
    }),
  },

  "image-watermark": {
    invokeCommand: "add_image_watermark",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      watermarkPath: params.watermarkPath,
      position: params.position || "右下",
      width: parseInt(params.width) || 100,
      opacity: parseFloat(params.opacity) || 0.8,
      customX: params.customX ? parseInt(params.customX) : null,
      customY: params.customY ? parseInt(params.customY) : null,
    }),
  },

  "video-watermark": {
    invokeCommand: "add_video_watermark",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      watermarkPath: params.watermarkPath,
      position: params.position || "右下",
      width: parseInt(params.width) || 100,
      opacity: parseFloat(params.opacity) || 0.8,
      customX: params.customX ? parseInt(params.customX) : null,
      customY: params.customY ? parseInt(params.customY) : null,
    }),
  },

  // 保持旧的映射名称以兼容性
  "add-text-watermark": {
    invokeCommand: "add_text_watermark",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      text: params.text || "Watermark",
      position: params.position || "bottom-right",
      fontSize: parseInt(params.fontSize) || 24,
      color: params.color || "#FFFFFF",
      opacity: parseFloat(params.opacity) || 0.8,
    }),
  },

  "add-image-watermark": {
    invokeCommand: "add_image_watermark",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      watermarkPath: params.watermarkPath,
      position: params.position || "bottom-right",
      scale: parseFloat(params.scale) || 0.2,
      opacity: parseFloat(params.opacity) || 0.8,
    }),
  },

  // 编码类动作
  "convert-format": {
    invokeCommand: "convert_format",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      format: params.format || "mp4",
      quality: params.quality || "medium",
    }),
  },

  "adjust-bitrate": {
    invokeCommand: "adjust_bitrate",
    buildParams: (params: ActionParams, context: ActionContext) => {
      let bitrate = "2000k"; // 默认码率

      if (params.bitrateType === "custom") {
        // 自定义码率，使用用户输入的值
        bitrate = `${params.customBitrate || 2000}k`;
      } else {
        // 预设码率，直接使用预设值
        bitrate = params.bitrateType || "2000k";
      }

      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        bitrate: bitrate,
      };
    },
  },

  "adjust-resolution": {
    invokeCommand: "adjust_resolution",
    buildParams: (params: ActionParams, context: ActionContext) => {
      let width = 1920;
      let height = 1080;

      // 根据分辨率类型确定宽高
      switch (params.resolutionType) {
        case "480p":
          width = 854;
          height = 480;
          break;
        case "720p":
          width = 1280;
          height = 720;
          break;
        case "1080p":
          width = 1920;
          height = 1080;
          break;
        case "1440p":
          width = 2560;
          height = 1440;
          break;
        case "4K":
          width = 3840;
          height = 2160;
          break;
        case "custom":
          width = parseInt(params.customWidth) || 1920;
          height = parseInt(params.customHeight) || 1080;
          break;
      }

      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        width: width,
        height: height,
      };
    },
  },

  "compress-video": {
    invokeCommand: "compress_video",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      compressionLevel: parseInt(params.compressionLevel) || 5,
    }),
  },

  // 保持旧的映射名称以兼容性
  "change-format": {
    invokeCommand: "convert_format",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      format: params.format || "mp4",
      quality: params.quality || "medium",
    }),
  },

  // 滤镜类动作
  grayscale: {
    invokeCommand: "filter_grayscale",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
    }),
  },

  sepia: {
    invokeCommand: "filter_sepia",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
    }),
  },

  emboss: {
    invokeCommand: "filter_emboss",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
    }),
  },

  sketch: {
    invokeCommand: "filter_sketch",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      mode: params.mode || "gray",
    }),
  },

  "oil-painting": {
    invokeCommand: "filter_oilpaint",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      intensity: parseFloat(params.intensity) || 10.0,
    }),
  },

  mosaic: {
    invokeCommand: "filter_mosaic",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      blockSize: parseInt(params.blockSize) || 10,
    }),
  },

  pixelate: {
    invokeCommand: "filter_pixelate",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      pixelSize: parseInt(params.pixelSize) || 10,
    }),
  },

  "edge-detection": {
    invokeCommand: "filter_edge_detect",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
    }),
  },

  "cold-tone": {
    invokeCommand: "filter_cool",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      strength: parseFloat(params.strength) || 2.0,
    }),
  },

  "warm-tone": {
    invokeCommand: "filter_warm",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      strength: parseFloat(params.strength) || 2.0,
    }),
  },

  // 图片相关动作
  "add-cover-image": {
    invokeCommand: "add_cover_image",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      imagePath: params.imagePath,
      duration: parseFloat(params.duration) || 3.0,
    }),
  },

  "add-end-image": {
    invokeCommand: "add_end_image",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      outputPath: context.outputPath,
      imagePath: params.imagePath,
      duration: parseFloat(params.duration) || 3.0,
    }),
  },

  "image-to-video": {
    invokeCommand: "image_to_video",
    buildParams: (params: ActionParams, context: ActionContext) => {
      let width = 1280;
      let height = 720;
      if (params.resolution === "480p") {
        width = 854;
        height = 480;
      } else if (params.resolution === "720p") {
        width = 1280;
        height = 720;
      } else if (params.resolution === "1080p") {
        width = 1920;
        height = 1080;
      } else if (params.resolution === "custom") {
        width = parseInt(params.customWidth) || 1280;
        height = parseInt(params.customHeight) || 720;
      }
      return {
        inputPath: context.inputPath,
        outputPath: context.outputPath,
        duration: parseFloat(params.duration) || 5.0,
        width,
        height,
      };
    },
  },

  "video-to-image": {
    invokeCommand: "video_to_images",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      count: parseInt(params.count) || 5,
      format: params.format || "jpg",
      method: params.method || "uniform",
      quality: params.format === "png" ? null : parseInt(params.quality) || 85,
      width: params.useCustomSize ? parseInt(params.customWidth) || null : null,
      height: null, // 高度自动按比例调整
    }),
  },

  "video-to-gif": {
    invokeCommand: "video_to_gif",
    buildParams: (params: ActionParams, context: ActionContext) => ({
      inputPath: context.inputPath,
      format: params.format || "gif",
      startTime: parseFloat(params.startTime) || 0,
      duration: parseFloat(params.duration) || 5,
      fps: parseFloat(params.fps) || 10,
      width: params.useCustomSize ? parseInt(params.customWidth) || null : null,
      height: params.useCustomSize
        ? parseInt(params.customHeight) || null
        : null,
      quality: parseInt(params.quality) || 80,
      loopCount: parseInt(params.loopCount) || 0,
    }),
  },

  "batch-image-to-video": {
    invokeCommand: "batch_images_to_video",
    buildParams: (params: ActionParams, context: ActionContext) => {
      let width = 1280;
      let height = 720;
      if (params.resolution === "480p") {
        width = 854;
        height = 480;
      } else if (params.resolution === "720p") {
        width = 1280;
        height = 720;
      } else if (params.resolution === "1080p") {
        width = 1920;
        height = 1080;
      } else if (params.resolution === "custom") {
        width = parseInt(params.customWidth) || 1280;
        height = parseInt(params.customHeight) || 720;
      }
      return {
        folderPath: context.inputPath,
        outputPath: context.outputPath,
        durationMode: params.durationMode || "per_image",
        durationValue: parseFloat(params.durationValue) || 2.0,
        width,
        height,
        transitionType: params.transitionType || "fade",
        transitionDuration: parseFloat(params.transitionDuration) || 0.5,
      };
    },
  },
};

// 工具函数：为动作添加映射信息
export function applyActionMapping(action: any): any {
  const mapping = actionMappings[action.id];
  if (mapping) {
    return {
      ...action,
      invokeCommand: mapping.invokeCommand,
      buildParams: mapping.buildParams,
    };
  }
  console.warn(`No mapping found for action: ${action.id}`);
  return action;
}
