// 滤镜类动作定�?

import { CategoryIds, ActionIds } from "../../constants";
import type { Action } from "../../types/action";
import { applyActionMapping } from "../action-mappings";

// 滤镜类动作定�?
const rawFilterActions = [
  {
    id: ActionIds.GRAYSCALE,
    nameKey: "actions.grayscale.name",
    descriptionKey: "actions.grayscale.description",
    categoryId: CategoryIds.FILTER,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 10,
    params: [],
  },
  {
    id: ActionIds.SEPIA,
    nameKey: "actions.sepia.name",
    descriptionKey: "actions.sepia.description",
    categoryId: CategoryIds.FILTER,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 20,
    params: [],
  },
  {
    id: ActionIds.EMBOSS,
    nameKey: "actions.emboss.name",
    descriptionKey: "actions.emboss.description",
    categoryId: CategoryIds.FILTER,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 30,
    params: [],
  },
  {
    id: ActionIds.SKETCH,
    nameKey: "actions.sketch.name",
    descriptionKey: "actions.sketch.description",
    categoryId: CategoryIds.FILTER,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 40,
    params: [
      {
        key: "mode",
        type: "select",
        nameKey: "actions.sketch.params.mode",
        required: false,
        defaultValue: "gray",
        options: [
          {
            value: "gray",
            labelKey: "actions.sketch.params.mode_options.gray",
          },
          {
            value: "color",
            labelKey: "actions.sketch.params.mode_options.color",
          },
        ],
      },
    ],
  },
  {
    id: ActionIds.OIL_PAINTING,
    nameKey: "actions.oil_painting.name",
    descriptionKey: "actions.oil_painting.description",
    categoryId: CategoryIds.FILTER,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 50,
    params: [
      {
        key: "intensity",
        type: "number",
        nameKey: "actions.oil_painting.params.intensity",
        required: false,
        defaultValue: 2.0,
        min: 0.1,
        max: 10.0,
        step: 0.1,
      },
    ],
  },
  {
    id: ActionIds.MOSAIC,
    nameKey: "actions.mosaic.name",
    descriptionKey: "actions.mosaic.description",
    categoryId: CategoryIds.FILTER,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 60,
    params: [
      {
        key: "blockSize",
        type: "range",
        nameKey: "actions.mosaic.params.blockSize",
        required: true,
        defaultValue: 10,
        min: 2,
        max: 50,
        step: 1,
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (params.blockSize < 2 || params.blockSize > 50) {
        errors.push("actions.mosaic.errors.invalid_block_size");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.PIXELATE,
    nameKey: "actions.pixelate.name",
    descriptionKey: "actions.pixelate.description",
    categoryId: CategoryIds.FILTER,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 70,
    params: [
      {
        key: "pixelSize",
        type: "number",
        nameKey: "actions.pixelate.params.pixelSize",
        required: false,
        defaultValue: 10,
        min: 2,
        max: 50,
        step: 1,
      },
    ],
  },
  {
    id: ActionIds.EDGE_DETECTION,
    nameKey: "actions.edge_detection.name",
    descriptionKey: "actions.edge_detection.description",
    categoryId: CategoryIds.FILTER,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 80,
    params: [],
  },
  {
    id: ActionIds.VINTAGE_FILTER,
    nameKey: "actions.vintage_filter.name",
    descriptionKey: "actions.vintage_filter.description",
    categoryId: CategoryIds.FILTER,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 90,
    params: [],
  },
  {
    id: ActionIds.COLD_TONE,
    nameKey: "actions.cold_tone.name",
    descriptionKey: "actions.cold_tone.description",
    categoryId: CategoryIds.FILTER,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 100,
    params: [
      {
        key: "strength",
        type: "number",
        nameKey: "actions.cold_tone.params.strength",
        required: false,
        defaultValue: 0.5,
        min: 0.0,
        max: 5.0,
        step: 0.1,
      },
    ],
  },
  {
    id: ActionIds.WARM_TONE,
    nameKey: "actions.warm_tone.name",
    descriptionKey: "actions.warm_tone.description",
    categoryId: CategoryIds.FILTER,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 110,
    params: [
      {
        key: "strength",
        type: "number",
        nameKey: "actions.warm_tone.params.strength",
        required: false,
        defaultValue: 0.5,
        min: 0.0,
        max: 5.0,
        step: 0.1,
      },
    ],
  },
];

// 应用映射并导�?
export const filterActions: Action[] = rawFilterActions.map(applyActionMapping);
