// 动作事件常量定义

export const ActionEvents = {
  // 裁剪相关事件
  TRIM_PROGRESS: "TRIM_PROGRESS",
  TRIM_START: "TRIM_START",
  TRIM_COMPLETE: "TRIM_COMPLETE",
  TRIM_ERROR: "TRIM_ERROR",

  // 颜色调整相关事件
  COLOR_PROGRESS: "COLOR_PROGRESS",
  COLOR_START: "COLOR_START",
  COLOR_COMPLETE: "COLOR_COMPLETE",
  COLOR_ERROR: "COLOR_ERROR",

  // 音频处理相关事件
  AUDIO_PROGRESS: "AUDIO_PROGRESS",
  AUDIO_START: "AUDIO_START",
  AUDIO_COMPLETE: "AUDIO_COMPLETE",
  AUDIO_ERROR: "AUDIO_ERROR",

  // 裁剪画面相关事件
  CROP_PROGRESS: "CROP_PROGRESS",
  CROP_START: "CROP_START",
  CROP_COMPLETE: "CROP_COMPLETE",
  CROP_ERROR: "CROP_ERROR",

  // 变换相关事件
  TRANSFORM_PROGRESS: "TRANSFORM_PROGRESS",
  TRANSFORM_START: "TRANSFORM_START",
  TRANSFORM_COMPLETE: "TRANSFORM_COMPLETE",
  TRANSFORM_ERROR: "TRANSFORM_ERROR",

  // 特效相关事件
  EFFECT_PROGRESS: "EFFECT_PROGRESS",
  EFFECT_START: "EFFECT_START",
  EFFECT_COMPLETE: "EFFECT_COMPLETE",
  EFFECT_ERROR: "EFFECT_ERROR",

  // 水印相关事件
  WATERMARK_PROGRESS: "WATERMARK_PROGRESS",
  WATERMARK_START: "WATERMARK_START",
  WATERMARK_COMPLETE: "WATERMARK_COMPLETE",
  WATERMARK_ERROR: "WATERMARK_ERROR",

  // 编码相关事件
  ENCODE_PROGRESS: "ENCODE_PROGRESS",
  ENCODE_START: "ENCODE_START",
  ENCODE_COMPLETE: "ENCODE_COMPLETE",
  ENCODE_ERROR: "ENCODE_ERROR",

  // 视频转图片相关事件
  VIDEOTOIMAGE_PROGRESS: "VIDEOTOIMAGE_PROGRESS",
  VIDEOTOIMAGE_START: "VIDEOTOIMAGE_START",
  VIDEOTOIMAGE_COMPLETE: "VIDEOTOIMAGE_COMPLETE",
  VIDEOTOIMAGE_ERROR: "VIDEOTOIMAGE_ERROR",

  // 视频转GIF相关事件
  VIDEOTOGIF_PROGRESS: "VIDEOTOGIF_PROGRESS",
  VIDEOTOGIF_START: "VIDEOTOGIF_START",
  VIDEOTOGIF_COMPLETE: "VIDEOTOGIF_COMPLETE",
  VIDEOTOGIF_ERROR: "VIDEOTOGIF_ERROR",

  // 批量图片转视频相关事件
  BATCH_IMAGE_PROGRESS: "BATCH_IMAGE_PROGRESS",
  BATCH_IMAGE_START: "BATCH_IMAGE_START",
  BATCH_IMAGE_COMPLETE: "BATCH_IMAGE_COMPLETE",
  BATCH_IMAGE_ERROR: "BATCH_IMAGE_ERROR",

  // 图片转视频相关事件
  IMAGE_TO_VIDEO_PROGRESS: "IMAGE_TO_VIDEO_PROGRESS",
  IMAGE_TO_VIDEO_START: "IMAGE_TO_VIDEO_START",
  IMAGE_TO_VIDEO_COMPLETE: "IMAGE_TO_VIDEO_COMPLETE",
  IMAGE_TO_VIDEO_ERROR: "IMAGE_TO_VIDEO_ERROR",

  // 滤镜相关事件
  FILTER_PROGRESS: "FILTER_PROGRESS",
  FILTER_START: "FILTER_START",
  FILTER_COMPLETE: "FILTER_COMPLETE",
  FILTER_ERROR: "FILTER_ERROR",

  // 通用事件
  PROCESSING_START: "PROCESSING_START",
  PROCESSING_COMPLETE: "PROCESSING_COMPLETE",
  PROCESSING_ERROR: "PROCESSING_ERROR",
  PROCESSING_CANCELLED: "PROCESSING_CANCELLED",
} as const;

export type ActionEventType = (typeof ActionEvents)[keyof typeof ActionEvents];
